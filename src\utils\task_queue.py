#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后台任务队列系统
用于处理异步翻译任务
"""

import os
import json
import time
import threading
import logging
from datetime import datetime
from typing import Dict, List, Optional
from queue import Queue, Empty

from src.models.translation_job import TranslationJob
from src.translator.gemini_translator import GeminiTranslator
from src.translator.mock_translator import MockTranslator
from src.loader.multi_format_loader import MultiFormatLoader
from src.generator.multi_format_generator import MultiFormatGenerator

logger = logging.getLogger(__name__)


class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self):
        self.queue = Queue()
        self.workers = []
        self.running = False
        self.worker_count = 2  # 并发工作线程数
        
    def start(self):
        """启动任务队列"""
        if self.running:
            return
            
        self.running = True
        logger.info(f"启动任务队列，工作线程数: {self.worker_count}")
        
        # 启动工作线程
        for i in range(self.worker_count):
            worker = threading.Thread(target=self._worker, name=f"TaskWorker-{i+1}")
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
            
        # 启动时加载待处理的任务
        self._load_pending_jobs()
    
    def stop(self):
        """停止任务队列"""
        self.running = False
        logger.info("停止任务队列")
    
    def add_job(self, job_id: str):
        """添加任务到队列"""
        self.queue.put(job_id)
        logger.info(f"任务已添加到队列: {job_id}")
    
    def _load_pending_jobs(self):
        """加载所有待处理的任务"""
        jobs_dir = 'data/jobs'
        if not os.path.exists(jobs_dir):
            return
            
        for filename in os.listdir(jobs_dir):
            if filename.endswith('.json'):
                job_id = filename[:-5]  # 移除.json后缀
                job = TranslationJob.load(job_id)
                if job and job.status == 'pending':
                    self.add_job(job_id)
                    logger.info(f"重新加载待处理任务: {job_id}")
    
    def _worker(self):
        """工作线程"""
        worker_name = threading.current_thread().name
        logger.info(f"工作线程启动: {worker_name}")
        
        while self.running:
            try:
                # 从队列获取任务，超时1秒
                job_id = self.queue.get(timeout=1)
                logger.info(f"[{worker_name}] 开始处理任务: {job_id}")
                
                # 处理任务
                success = self._process_job(job_id)
                
                if success:
                    logger.info(f"[{worker_name}] 任务完成: {job_id}")
                else:
                    logger.error(f"[{worker_name}] 任务失败: {job_id}")
                
                # 标记任务完成
                self.queue.task_done()
                
            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"[{worker_name}] 工作线程异常: {str(e)}")
                continue
        
        logger.info(f"工作线程停止: {worker_name}")
    
    def _process_job(self, job_id: str) -> bool:
        """处理单个翻译任务"""
        try:
            # 加载任务
            job = TranslationJob.load(job_id)
            if not job:
                logger.error(f"任务不存在: {job_id}")
                return False
            
            if job.status != 'pending':
                logger.warning(f"任务状态不是pending: {job_id}, 当前状态: {job.status}")
                return False
            
            # 更新任务状态为处理中
            job.status = 'processing'
            job.started_at = datetime.now()
            job.save()
            
            # 初始化加载器
            multi_loader = MultiFormatLoader()
            
            # 加载文件内容
            logger.info(f"加载文件: {job.file_path}")
            chapters, original_format = multi_loader.load_file(job.file_path)
            logger.info(f"文件加载成功，格式: {original_format}, 章节数: {len(chapters)}")
            
            # 初始化翻译器
            use_mock = os.getenv('USE_MOCK_TRANSLATOR', 'false').lower() == 'true'
            if use_mock:
                logger.info("使用模拟翻译器")
                translator = MockTranslator(target_language=job.target_language, style=job.style)
            else:
                logger.info("使用Gemini翻译器")
                translator = GeminiTranslator(target_language=job.target_language, style=job.style)
            
            # 翻译章节
            logger.info(f"开始翻译 {len(chapters)} 个章节")
            translated_chapters = []
            for i, chapter in enumerate(chapters):
                try:
                    logger.info(f"翻译进度: {i+1}/{len(chapters)}")
                    # 限制单个章节长度
                    if len(chapter) > 5000:
                        chapter = chapter[:5000] + "..."
                    
                    translated_chapter = translator.translate(chapter)
                    translated_chapters.append(translated_chapter)
                except Exception as e:
                    logger.error(f"翻译第{i+1}章节失败: {str(e)}")
                    translated_chapters.append(f"[翻译失败] {chapter}")
                    continue
            
            # 生成双语文档
            logger.info("生成双语文档")
            multi_generator = MultiFormatGenerator()
            output_dir = f"outputs/{job_id}"

            # 生成基础文件名
            base_filename = f"{os.path.splitext(job.original_filename)[0]}_bilingual"

            # 生成三种格式：EPUB、PDF、Word
            target_formats = ['epub', 'pdf', 'docx']
            output_files = multi_generator.generate_all_formats(
                original_chapters=chapters,
                translated_chapters=translated_chapters,
                output_dir=output_dir,
                base_filename=base_filename,
                original_format=original_format,
                target_formats=target_formats
            )
            
            # 更新任务状态为完成
            job.status = 'completed'
            job.output_path = output_dir
            job.output_files = output_files
            job.completed_at = datetime.now()
            job.save()
            
            # 清理临时文件
            multi_loader.cleanup()
            multi_generator.cleanup()
            
            logger.info(f"翻译任务完成: {job_id}")
            return True
            
        except Exception as e:
            # 更新任务状态为失败
            try:
                job = TranslationJob.load(job_id)
                if job:
                    job.status = 'failed'
                    job.error_message = str(e)
                    job.save()
            except:
                pass
            
            logger.error(f"翻译任务失败: {job_id}, 错误: {str(e)}")
            return False


# 全局任务队列实例
task_queue = TaskQueue()


def start_task_queue():
    """启动任务队列"""
    task_queue.start()


def stop_task_queue():
    """停止任务队列"""
    task_queue.stop()


def add_translation_job(job_id: str):
    """添加翻译任务到队列"""
    task_queue.add_job(job_id)
