#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译任务模型
"""

import json
import os
from datetime import datetime
from typing import Optional


class TranslationJob:
    """翻译任务类"""
    
    def __init__(self, job_id: str, upload_id: str, original_filename: str,
                 file_path: str, target_language: str, style: str, status: str = 'pending'):
        self.job_id = job_id
        self.upload_id = upload_id
        self.original_filename = original_filename
        self.file_path = file_path
        self.target_language = target_language
        self.style = style
        self.status = status  # pending, processing, completed, failed
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.output_path = None
        self.output_files = {}  # 格式到文件路径的映射
        self.error_message = None
        self.cost_points = 0
        self.user_id = None  # 关联的用户ID
    
    def to_dict(self):
        """转换为字典"""
        return {
            'job_id': self.job_id,
            'upload_id': self.upload_id,
            'original_filename': self.original_filename,
            'file_path': self.file_path,
            'target_language': self.target_language,
            'style': self.style,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'output_path': self.output_path,
            'output_files': self.output_files,
            'error_message': self.error_message,
            'cost_points': self.cost_points,
            'user_id': self.user_id
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建任务对象"""
        job = cls(
            data['job_id'], data['upload_id'], data['original_filename'],
            data['file_path'], data['target_language'], data['style'], data['status']
        )
        job.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            job.started_at = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            job.completed_at = datetime.fromisoformat(data['completed_at'])
        job.output_path = data.get('output_path')
        job.output_files = data.get('output_files', {})
        job.error_message = data.get('error_message')
        job.cost_points = data.get('cost_points', 0)
        job.user_id = data.get('user_id')
        return job
    
    def save(self):
        """保存任务数据"""
        os.makedirs('data/jobs', exist_ok=True)
        file_path = f'data/jobs/{self.job_id}.json'
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load(cls, job_id: str) -> Optional['TranslationJob']:
        """加载任务数据"""
        file_path = f'data/jobs/{job_id}.json'
        if not os.path.exists(file_path):
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls.from_dict(data)
        except Exception:
            return None
    
    def calculate_cost(self, word_count: int) -> int:
        """计算翻译成本（积分）"""
        # 每100词2积分
        import math
        return math.ceil(word_count / 100) * 2
