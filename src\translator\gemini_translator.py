#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini翻译器实现
基于参考实现，集成硬编码的API配置
"""

import time
import requests
import json
import logging
import re
from .base_translator import BaseTranslator

logger = logging.getLogger(__name__)


class GeminiTranslator(BaseTranslator):
    """Google Gemini翻译器 - 集成硬编码API配置"""

    # 硬编码的API配置（来自参考实现）
    API_URL = "https://fromozu-gemini-balance.hf.space/v1/chat/completions"
    API_KEY = "123qweASD"  # 参考实现中的固定密钥

    def __init__(self, api_key=None, target_language="中文", style='casual'):
        # 忽略传入的api_key，使用硬编码配置
        super().__init__(self.API_KEY, target_language, style)
        self.rate_limit_delay = 2  # 减少延迟时间
        self.max_retries = 3  # 减少重试次数，避免长时间阻塞
        self.retry_delay = 5  # 减少重试延迟
        
    def get_casual_prompt(self):
        """获取轻松风格的翻译提示词"""
        return """
You are a professional translation assistant. Your task is to translate text accurately, naturally, and with emotional impact into {language}. You excel at capturing the original tone and emotion and seamlessly infusing them into the translation. Translate `{text}` into {language} with high accuracy. Your output **must contain the translation only**—no prefaces, explanations, or any non-translation content.

Translation rules (strictly follow):

1. **Style**
   - Target a natural, everyday spoken register in {language}.
   - Keep the wording smooth and idiomatic; avoid stiff formality or machine-translation traces.

2. **Tone & Emotion**
   - Use a slightly informal voice that conveys the original author's enthusiasm and sincere appreciation.

3. **Expression Techniques**
   - Skillfully weave in vivid colloquialisms and idiomatic phrases to make the text lively and conversational.

4. **Translation Strategy**
   - Steer clear of rigid, word-for-word renderings.
   - Grasp the core meaning and emotion, then reorganize it into fluent {language} that captures both spirit and form.

5. **Proper Noun Handling**
   - For human names, product names, software names, technical terms, model names, brand names, code identifiers, or specific abbreviations, **keep them in their original form or add the original form in parentheses if translation is necessary**.
   - Embed these terms naturally in the {language} sentence.

6. **Overall Goal**
   - Deliver a highly natural, colloquial {language} translation that reads like a heartfelt recommendation from a real user, not machine output.

Always return a high-quality, idiomatic {language} translation **and nothing else**.
"""

    def get_faithful_prompt(self):
        """获取严谨忠实风格的翻译提示词"""
        return """
You are a professional academic translator. Your task is to translate text with absolute accuracy and fidelity to the original meaning. Translate `{text}` into {language} with the highest precision. Your output **must contain the translation only**—no prefaces, explanations, or any non-translation content.

Translation rules (strictly follow):

1. **Accuracy First**
   - Maintain complete fidelity to the original meaning, structure, and intent.
   - Preserve the author's original tone and register as closely as possible.

2. **Structural Preservation**
   - Keep sentence structures and paragraph organization similar to the original.
   - Maintain the logical flow and argument structure of the source text.

3. **Terminology Consistency**
   - Use consistent terminology throughout the translation.
   - Preserve technical terms, academic vocabulary, and specialized language.

4. **Formal Register**
   - Use appropriate formal language that matches the original text's register.
   - Avoid overly casual expressions unless present in the original.

5. **Proper Noun Handling**
   - Keep all proper nouns (names, places, organizations, technical terms, brand names) in their original form.
   - Add translations in parentheses only when necessary for clarity.

6. **Cultural Sensitivity**
   - Preserve cultural references and context from the original text.
   - Explain cultural concepts only through accurate translation, not interpretation.

7. **Overall Goal**
   - Deliver a precise, scholarly {language} translation that maintains complete fidelity to the original text's meaning, structure, and academic integrity.

Always return a precise, faithful {language} translation **and nothing else**.
"""

    def get_detailed_prompt(self):
        """根据翻译风格获取相应的提示词"""
        if self.style == 'faithful':
            return self.get_faithful_prompt()
        else:  # 默认使用轻松风格 (casual)
            return self.get_casual_prompt()
    
    def translate(self, text):
        """翻译文本 - 使用参考实现的完整逻辑"""
        if self._is_special_text(text):
            return text

        # 构建请求头（使用硬编码的API密钥）
        headers = {
            "Authorization": f"Bearer {self.API_KEY}",
            "Content-Type": "application/json"
        }

        # 使用参考实现的提示词格式
        prompt = self.get_detailed_prompt().format(text=text, language=self.target_language)

        # 使用参考实现的完整payload配置
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "model": "gemini-2.0-flash",
            "temperature": 0.7,
            "stream": False,
            "tools": [],
            "max_tokens": 8192,
            "stop": [],
            "top_p": 0.9,
            "top_k": 40
        }

        # 使用有限重试逻辑，避免无限循环
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                logger.info(f"正在翻译 (尝试 {retry_count + 1}/{self.max_retries}): {text[:100]}...")

                response = requests.post(self.API_URL, headers=headers, json=payload, timeout=30)
                response.raise_for_status()
                response_data = response.json()

                translated_text = response_data['choices'][0]['message']['content']
                t_text = translated_text.strip()

                # 参考实现中的输出格式化
                formatted_text = re.sub(r"\n{3,}", "\n\n", t_text)
                logger.info(f"[翻译完成] {formatted_text[:100]}...")

                # 参考实现中的速率限制
                time.sleep(self.rate_limit_delay)

                return t_text

            except requests.exceptions.Timeout:
                logger.warning(f"翻译请求超时 (尝试 {retry_count + 1}/{self.max_retries})")
                retry_count += 1
                if retry_count < self.max_retries:
                    time.sleep(self.retry_delay)
                continue

            except requests.exceptions.RequestException as e:
                logger.warning(f"请求错误 (尝试 {retry_count + 1}/{self.max_retries}): {str(e)}")
                retry_count += 1
                if retry_count < self.max_retries:
                    time.sleep(self.retry_delay)
                continue

            except (ValueError, KeyError) as e:
                logger.warning(f"数据解析错误 (尝试 {retry_count + 1}/{self.max_retries}): {str(e)}")
                retry_count += 1
                if retry_count < self.max_retries:
                    time.sleep(self.retry_delay)
                continue

            except Exception as e:
                logger.error(f"其它错误 (尝试 {retry_count + 1}/{self.max_retries}): {str(e)}")
                retry_count += 1
                if retry_count < self.max_retries:
                    time.sleep(self.retry_delay)
                continue

        # 如果所有重试都失败，返回原文
        logger.error(f"翻译失败，已达到最大重试次数 {self.max_retries}")
        return f"[翻译失败] {text}"
